import { SequelizeModelController } from "../adaptors/sequelizer";
import { IEntitySchemaOptions, IModelFeilds, FieldDefinition } from "../types";

export class EntitySchema<T extends Record<string, FieldDefinition>> {
  public readonly entityName: string;
  public readonly attributes: IModelFeilds;
  private readonly options: IEntitySchemaOptions;
  private sequelizerModel: SequelizeModelController;

  constructor(
    entityName: string,
    attributes: IModelFeilds,
    sequelizerModel: SequelizeModelController,
    options?: IEntitySchemaOptions
  ) {
    this.entityName = entityName;
    this.attributes = attributes;
    this.sequelizerModel = sequelizerModel;
    this.options = options || {};

    // Return a proxy to enable field access validation and autocomplete
    return new Proxy(this, {
      get: (target, prop: string | symbol) => {
        // If it's a string property and exists in schema, return the field definition
        if (typeof prop === "string" && prop in this.attributes) {
          return this.attributes[prop];
        }

        // Otherwise, return the actual method/property from the class
        return (target as any)[prop];
      },
    });
  }

  public getEntityName() {
    return this.entityName;
  }

  public getAttributes() {
    return this.attributes;
  }

  public getOptions() {
    return this.options;
  }

  public setSequelizerModel(model: SequelizeModelController) {
    return (this.sequelizerModel = model);
  }

  public getSequelizerEntitySchema(): SequelizeModelController {
    return this.sequelizerModel;
  }

  /**
   * Get list of available field names
   */
  public getFieldNames(): string[] {
    return Object.keys(this.attributes);
  }

  /**
   * Check if a field exists
   */
  public hasField(fieldName: string): boolean {
    return fieldName in this.attributes;
  }

  /**
   * Get field metadata safely
   */
  public getField(fieldName: string) {
    if (!this.hasField(fieldName)) {
      const availableFields = this.getFieldNames().join(", ");
      throw new Error(
        `Field '${fieldName}' does not exist. Available fields: ${availableFields}`
      );
    }
    return this.attributes[fieldName];
  }

  validate(data: any): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    Object.entries(this.attributes).forEach(([fieldName, definition]) => {
      const value = data[fieldName];

      // Check for null/undefined on non-nullable fields
      if (!definition.isNullable && (value === null || value === undefined)) {
        errors.push(`Field '${fieldName}' cannot be null or undefined`);
      }

      // Type checking (basic)
      if (value !== null && value !== undefined) {
        const expectedType = definition.dataType;
        let isValidType = false;

        switch (expectedType) {
          case "int":
          case "float":
          case "double":
            isValidType = typeof value === "number";
            break;
          case "string":
          case "uuid":
          case "text":
            isValidType = typeof value === "string";
            break;
          case "boolean":
            isValidType = typeof value === "boolean";
            break;
          case "date":
            isValidType = value instanceof Date;
            break;
        }

        if (!isValidType) {
          errors.push(
            `Field '${fieldName}' should be of type '${expectedType}'`
          );
        }
      }
    });

    return {
      isValid: errors.length === 0,
      errors,
    };
  }
}
