import {
  IModelFeilds,
  IDbConfig,
  IEntitySchemaOptions,
  Schema,
  EntitySchemaModel,
} from "../types";
import { EntitySchema } from "./entitySchema";
import { SequelizerAdaptor } from "../adaptors";

export class DataSource {
  private dbConfig = {};
  private sequelizerAdaptor: SequelizerAdaptor;
  public entityMap: Map<string, EntitySchema<IModelFeilds>> = new Map();

  constructor(dbConfig: IDbConfig) {
    this.dbConfig = dbConfig;
    this.sequelizerAdaptor = new SequelizerAdaptor(dbConfig);
  }

  public defineEntity<T extends IModelFeilds>(
    entityName: string,
    attributes: Schema<T>,
    options?: IEntitySchemaOptions
  ): EntitySchemaModel<T> {
    const sequelizerModel = this.sequelizerAdaptor.define(
      entityName,
      attributes,
      options
    );

    const model = new EntitySchema(
      entityName,
      attributes,
      sequelizerModel,
      options
    );

    this.entityMap.set(entityName, model);

    return model as EntitySchemaModel<T>;
  }

  public getEntity<T extends IModelFeilds>(
    entityName: string
  ): EntitySchemaModel<T> | undefined {
    if (!this.entityMap.has(entityName)) {
      return undefined;
    }
    return this.entityMap.get(entityName) as EntitySchemaModel<T>;
  }

  public getDbConfig() {
    return this.dbConfig;
  }
}
