function parseFilter(filterClause: string, tableName: string = ""): any {
  console.log("------filterClause--->", filterClause);
  const result = parseODataExpression(filterClause);
  return result;
}

function parseODataExpression(input: string): any {
  const tokens = tokenizeOData(input);
  console.log("------tokens--->", tokens);
  return parseTokens(tokens);
}

function tokenizeOData(input: string): string[] {
  const tokens: string[] = [];
  let i = 0;

  while (i < input.length) {
    // Skip whitespace
    if (input[i] === " ") {
      i++;
      continue;
    }

    // Handle string literals
    if (input[i] === "'" || input[i] === '"') {
      const quote = input[i];
      let str = quote;
      i++;
      while (i < input.length && input[i] !== quote) {
        str += input[i];
        i++;
      }
      if (i < input.length) {
        str += input[i]; // closing quote
        i++;
      }
      tokens.push(str);
      continue;
    }

    // Handle parentheses and commas
    if (input[i] === "(" || input[i] === ")" || input[i] === ",") {
      tokens.push(input[i]);
      i++;
      continue;
    }

    // Handle words/identifiers/operators
    let word = "";
    while (
      i < input.length &&
      input[i] !== " " &&
      input[i] !== "(" &&
      input[i] !== ")" &&
      input[i] !== "," &&
      input[i] !== "'" &&
      input[i] !== '"'
    ) {
      word += input[i];
      i++;
    }

    if (word) {
      tokens.push(word);
    }
  }

  return tokens;
}

function parseTokens(tokens: string[]): any {
  let index = 0;

  function parseLogicalExpression(): any {
    let left = parseCondition();

    const conditions = [left];
    let logicalOp = "and"; // default

    while (
      index < tokens.length &&
      (tokens[index]?.toLowerCase() === "and" ||
        tokens[index]?.toLowerCase() === "or")
    ) {
      logicalOp = tokens[index].toLowerCase();
      index++; // consume logical operator
      const right = parseCondition();
      conditions.push(right);
    }

    if (conditions.length === 1) {
      return conditions[0];
    }

    return {
      logicalOperator: logicalOp,
      conditions: conditions,
    };
  }

  function parseCondition(): any {
    // Handle NOT operator
    if (tokens[index]?.toLowerCase() === "not") {
      index++; // consume 'not'
      const condition = parseCondition();
      return {
        operator: "not",
        condition: condition,
      };
    }

    // Handle parentheses
    if (tokens[index] === "(") {
      index++; // consume '('
      const result = parseLogicalExpression();
      if (tokens[index] === ")") {
        index++; // consume ')'
      }
      return result;
    }

    // Check if this is a function call
    if (index + 1 < tokens.length && tokens[index + 1] === "(") {
      return parseFunctionCall();
    }

    // Regular field operator value
    return parseSimpleCondition();
  }

  function parseFunctionCall(): any {
    const functionName = tokens[index].toLowerCase();
    index++; // consume function name
    index++; // consume '('

    const args = [];

    // Parse function arguments
    while (index < tokens.length && tokens[index] !== ")") {
      if (tokens[index] === ",") {
        index++; // consume comma
        continue;
      }

      // Check if argument is another function call
      if (index + 1 < tokens.length && tokens[index + 1] === "(") {
        args.push(parseFunctionCall());
      } else {
        args.push(tokens[index]);
        index++;
      }
    }

    if (tokens[index] === ")") {
      index++; // consume closing ')'
    }

    // Handle different function types
    switch (functionName) {
      case "contains":
      case "startswith":
      case "endswith": {
        let field = args[0];
        let fieldStringOperator = null;

        // Check if first arg is a function result
        if (typeof args[0] === "object" && args[0].functionName) {
          field = args[0].field;
          fieldStringOperator = args[0].functionName;
        }

        const value = parseStringValue(args[1]);

        // Check for optional 'eq true/false'
        let expectedResult = true;
        if (index < tokens.length && tokens[index]?.toLowerCase() === "eq") {
          index++; // consume 'eq'
          expectedResult = tokens[index] === "true";
          index++; // consume boolean
        }

        const condition: any = {
          field,
          operator: functionName,
          value,
        };

        if (fieldStringOperator) {
          condition.fieldStringOperator = fieldStringOperator;
        }

        return condition;
      }

      case "length": {
        const field = args[0];

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error("Expected comparison operator after length function");
        }

        const operator = tokens[index];
        index++;
        const value = parseValue(tokens[index]);
        index++;

        return {
          field,
          operator,
          value,
          fieldStringOperator: "length",
        };
      }

      case "indexof": {
        const field = args[0];
        const searchValue = parseStringValue(args[1]);

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error(
            "Expected comparison operator after indexof function"
          );
        }

        const operator = tokens[index];
        index++;
        const value = parseValue(tokens[index]);
        index++;

        return {
          field,
          operator,
          value,
          fieldStringOperator: "indexof",
          stringSearchValue: searchValue,
        };
      }

      case "tolower":
      case "toupper": {
        const field = args[0];

        // This could be used standalone or as part of another function
        if (
          index < tokens.length &&
          (tokens[index]?.toLowerCase() === "eq" ||
            tokens[index]?.toLowerCase() === "ne" ||
            tokens[index]?.toLowerCase() === "gt" ||
            tokens[index]?.toLowerCase() === "ge" ||
            tokens[index]?.toLowerCase() === "lt" ||
            tokens[index]?.toLowerCase() === "le")
        ) {
          const operator = tokens[index];
          index++;
          const value = parseStringValue(tokens[index]);
          index++;

          return {
            field,
            operator,
            value,
            fieldStringOperator: functionName,
          };
        } else {
          // Return as function result for nested use
          return {
            functionName,
            field,
          };
        }
      }

      case "substring": {
        const field = args[0];
        const startIdx = parseValue(args[1]);
        const length = args.length > 2 ? parseValue(args[2]) : null;

        // Must be followed by comparison operator
        if (index >= tokens.length) {
          throw new Error(
            "Expected comparison operator after substring function"
          );
        }

        const operator = tokens[index];
        index++;
        const value = parseStringValue(tokens[index]);
        index++;

        const condition: any = {
          field,
          operator,
          value,
          fieldStringOperator: "substring",
          substringStart: startIdx,
        };

        if (length !== null) {
          condition.substringLength = length;
        }

        return condition;
      }

      default:
        throw new Error(`Unknown function: ${functionName}`);
    }
  }

  function parseSimpleCondition(): any {
    const field = tokens[index];
    index++;
    const operator = tokens[index];
    index++;
    const value = parseValue(tokens[index]);
    index++;

    return { field, operator, value };
  }

  function parseValue(token: string): any {
    if (!token) return token;

    if (token.startsWith("'") && token.endsWith("'")) {
      return token.slice(1, -1);
    }
    if (token.startsWith('"') && token.endsWith('"')) {
      return token.slice(1, -1);
    }

    const num = Number(token);
    if (!isNaN(num)) {
      return num;
    }

    if (token.toLowerCase() === "true") return true;
    if (token.toLowerCase() === "false") return false;
    if (token.toLowerCase() === "null") return null;

    return token;
  }

  function parseStringValue(token: string): any {
    return parseValue(token);
  }

  return parseLogicalExpression();
}

const problematicFilter =
  "contains(tolower(CompanyName),'tech') and length(ContactName) gt 5 and indexof(Email,'@gmail.com') ne -1";

const testFilter =
  "((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0)";

// Example usage:
const input =
  "((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0) and (not (length(userId) gt 5) and not email eq  akila) and (contains(tolower(CompanyName),'tech') or length(ContactName) gt 5 or indexof(Email,'@gmail.com') ne -1)";

const conditions = parseFilter(input);

const obj = {
  logicalOperator: "end",
  conditions: [
    {
      field: "CompanyName",
      operator: "contains",
      value: "tech",
      fieldStringOperator: "tolower",
    },
    {
      field: "ContactName",
      operator: "gt",
      value: 5,
      fieldStringOperator: "length",
    },
    {
      field: "Email",
      operator: "ne",
      value: -1,
      fieldStringOperator: "indexof",
      stringSearchValue: "@gmail.com",
    },
  ],
};

const query1 =
  "/Orders?$select=ProductId,,Name,Price&$expand=Customer($select=CustomerName,Email),OrderItems($select=ProductName,Quantity,UnitPrice)&$filter=Status ne 'Cancelled'";
const query2 =
  "/Products?$select=ProductId,Name,Price&$expand=Category($select=CategoryName,Description;$filter=IsActive eq true)&$filter=Price lt 1000";
const query3 =
  "/Customers?$expand=Orders($expand=OrderItems($expand=Product($select=Name,Category);$select=Quantity,UnitPrice);$select=OrderDate,Status)&$top=10";
const query4 =
  "/Employees?$filter=Age gt 25 and Salary le 75000.50 and IsManager eq false and Department in ('Sales','Marketing','Support') and HireDate gt datetime'2020-01-01T00:00:00Z'";
const query5 =
  "/Categories?$expand=Products($expand=OrderItems($expand=Order($select=OrderDate,CustomerName;$filter=Status eq 'Completed');$select=Quantity;$filter=Quantity gt 1);$select=Name,Price;$filter=Price gt 10;$orderby=Price desc)&$orderby=CategoryName";
const query6 =
  "/Products?$filter=((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0)&$select=Name,Category,Price,Rating";
const query7 =
  "/Products?$filter=((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Price gt 50)) and (InStock eq true and Rating ge 4.0)&$select=Name,Category,Price,Rating";
const query8 =
  "/Customers?$filter=contains(tolower(CompanyName),'tech') and length(ContactName) gt 5 and indexof(Email,'@gmail.com') ne -1&$orderby=CompanyName desc,ContactName";
const query9 =
  "/Orders?$expand=Customer($select=CustomerName,Country;$filter=Country eq 'USA'),OrderItems($expand=Product($select=Name,Category;$filter=Category ne 'Discontinued');$filter=Quantity gt 2 and UnitPrice lt 100)&$filter=OrderDate gt datetime'2023-06-01T00:00:00Z'";
const query10 = `/Products?$filter=Description eq 'High-quality "premium" product & accessories' and Name contains 'O''Reilly'`;
const query11 =
  "/Employees?$filter=Manager eq null and IsActive eq true and Salary ne null&$select=EmployeeId,Name,Department";
const query12 =
  "/Companies?$expand=Departments($expand=Employees($expand=Projects($expand=Tasks($select=TaskName,Status;$filter=Status eq 'InProgress');$select=ProjectName,StartDate);$select=FirstName,LastName,Position;$filter=IsActive eq true);$select=DepartmentName;$filter=Budget gt 100000)&$top=5";
const query13 =
  "/OrderItems?$filter=(Quantity * UnitPrice) gt 500 and (Quantity * UnitPrice * 0.1) le 100&$select=ProductName,Quantity,UnitPrice&$orderby=(Quantity * UnitPrice) desc";
const query14Filter =
  "?$filter=((Category eq 'Electronics' and Price gt 100) or (Category eq 'Clothing' and Department in ('Sales','Marketing','Support'))) and (InStock eq true and Rating ge 4.0) and (not (length(userId) gt 5) and not email eq  akila) and (contains(tolower(CompanyName),'tech') or length(ContactName) gt 5 or indexof(Email,'@gmail.com') ne -1)";

const query15 =
  "Customers?$expand=Orders($expand=CustomerOrder($select=OrderDate,CustomerName;$filter=Status eq 'Completed' and and Department in ('Sales','Marketing','Support'));$select=OrderID,OrderDate;$filter=Amount gt 100),Addresses($select=Street,City),User/UserOrders";

const query16 =
  "/Companies?$expand=Departments($expand=Employees($expand=Projects($expand=Tasks($select=TaskName,Status;$filter=Status eq 'InProgress');$select=ProjectName,StartDate);$select=FirstName,LastName,Position;$filter=IsActive eq true);$select=DepartmentName;$filter=Budget gt 100000)&$top=5&$select=UserName,Age,Email, Test";
