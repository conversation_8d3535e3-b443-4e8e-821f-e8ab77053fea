import { IControllerConfig, IMethod, IQuery } from "../types";
import { QueryParser } from "./query-parser";

export class ODataControler {
  public config: IControllerConfig;

  constructor(config: IControllerConfig) {
    this.config = config;
  }

  public get(query: QueryParser) {
    return this.queryble(query);
  }

  public post(query: IQuery): IQuery {
    return query;
  }

  public delete(query: IQuery): IQuery {
    return query;
  }

  public put(query: IQuery): IQuery {
    return query;
  }

  public getAllowedMethod(): IMethod[] {
    if (this.config.allowedMethod) {
      return this.config.allowedMethod;
    } else {
      return ["get"];
    }
  }

  public getEndpoint(): string {
    if (this.config.endpoint) {
      return this.config.endpoint;
    } else {
      return this.config.model.entityName;
    }
  }

  public queryble(query: QueryParser) {
    // this.getSequilzeModels(query);
    return { sucess: true };
  }
}
