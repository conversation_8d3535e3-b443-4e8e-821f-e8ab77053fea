import { DataSource, EntitySchema, ODataControler } from "../core";

export * from "./queryParser.types";
export * from "./entitySchema.types";

type Dialect =
  | "mysql"
  | "postgres"
  | "sqlite"
  | "mariadb"
  | "mssql"
  | "db2"
  | "snowflake"
  | "oracle";

interface PoolOptions {
  /**
   * Maximum number of connections in pool. Default is 5
   */
  max?: number;

  /**
   * Minimum number of connections in pool. Default is 0
   */
  min?: number;

  /**
   * The maximum time, in milliseconds, that a connection can be idle before being released
   */
  idle?: number;

  /**
   * The maximum time, in milliseconds, that pool will try to get connection before throwing error
   */
  acquire?: number;

  /**
   * The time interval, in milliseconds, after which sequelize-pool will remove idle connections.
   */
  evict?: number;

  /**
   * The number of times to use a connection before closing and replacing it.  Default is Infinity
   */
  maxUses?: number;
}

interface IDbConfig {
  database: string;
  username: string;
  password: string;
  host: string;
  dialect: Dialect;
  port: number;
  pool: PoolOptions;
  schema: string;
  ssl?: boolean;
  entities?: EntitySchema<any>[];
}

type IMethod = "get" | "post" | "put" | "delete";

interface IControllerConfig {
  endpoint?: string;
  allowedMethod?: IMethod[];
  model: EntitySchema<any>;
}

interface IQuery {}

interface IExpressRouterConfig {
  controllers: ODataControler[];
  dataSource: DataSource;
}

export {
  IDbConfig,
  Dialect,
  PoolOptions,
  IControllerConfig,
  IMethod,
  IQuery,
  IExpressRouterConfig,
};
