import { EntitySchema } from "../core";
import {
  DataTypes as SequelizeDataTypes,
  DataType as SequelizeDataTypeInterface,
} from "sequelize";

export const DataTypes = SequelizeDataTypes;
export type IDataType = SequelizeDataTypeInterface;

export interface FieldDefinition {
  // dataType: keyof DataTypeMap;
  dataType: IDataType;
  columnName?: string;
  isPrimaryKey?: boolean;
  isNullable?: boolean;
  isUnique?: boolean;
  isAutoIncrement?: boolean;
  onDelete?: "CASCADE" | "SET NULL" | "RESTRICT" | "NO ACTION";
  onUpdate?: "CASCADE" | "SET NULL" | "RESTRICT" | "NO ACTION";
  defaultValue?: string | number | Date | boolean;
}

export type IModelFeilds = Record<string, FieldDefinition>;

// Define the mapping from dataType strings to TypeScript types
export type DataTypeMap = {
  int: number;
  string: string;
  uuid: string;
  boolean: boolean;
  date: Date;
  float: number;
  double: number;
  text: string;
};

// Define the schema object type
export type Schema<T extends IModelFeilds> = T;

export type EntitySchemaModel<T extends Record<string, FieldDefinition>> = {
  [K in keyof T]: T[K];
} & EntitySchema<T>;

export interface IEntitySchemaOptions {}
