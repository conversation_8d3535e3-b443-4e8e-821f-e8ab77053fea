import {
  Sequelize,
  ModelAttributes,
  Model,
  DataTypes as SequelizeDataTypes,
  DataType as SequelizeDataTypeInterface,
  ModelCtor,
  Op,
} from "sequelize";
import { IModelFeilds, IDbConfig, IEntitySchemaOptions } from "../types";

type SequelizeModelController = ModelCtor<Model<any, any>>;

const OPERATOR_MAP = {
  eq: Op.eq,
  ne: Op.ne,
  gt: Op.gt,
  ge: Op.gte,
  lt: Op.lt,
  le: Op.lte,
  contains: Op.like,
  startswith: Op.startsWith,
  endswith: Op.endsWith,
  in: Op.in,
  and: Op.and,
  or: Op.or,
  not: Op.not,
};

const STRING_FUNCTION_MAP = {
  tolower: (value: string) => ({ [Op.iLike]: value.toLowerCase() }),
  toupper: (value: string) => ({ [Op.iLike]: value.toUpperCase() }),
  length: (operator: string, value: string) => {
    return { [OPERATOR_MAP[operator]]: value };
  },
  indexof: (searchValue: string, operator: string) => {
    // For PostgreSQL: position('searchValue' in field) > compareValue
    // This would need raw SQL
    return { [Op.like]: `%${searchValue}%` };
  },
  substring: (operator: string, value: string) => {
    // For PostgreSQL: substring(field, start, length) = value
    // This would need raw SQL
    return { [OPERATOR_MAP[operator]]: value };
  },
};

export class SequelizerAdaptor {
  private sequelize: Sequelize;

  constructor(dbConfig: IDbConfig) {
    this.sequelize = new Sequelize({
      database: dbConfig.database,
      username: dbConfig.username,
      password: dbConfig.password,
      host: dbConfig.host,
      dialect: dbConfig.dialect,
      port: dbConfig.port,
      pool: dbConfig.pool,
      schema: dbConfig.schema,
      ssl: dbConfig.ssl,
    });
    const User = this.sequelize.define("test", {
      id: {
        type: SequelizeDataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
      },
    });

    new User().validate({ fields: ["ids"] });
  }

  public define(
    modelName: string,
    attributes: IModelFeilds,
    options?: IEntitySchemaOptions
  ) {
    const formattedAttributes = this.formatAttributes(attributes);
    const model: SequelizeModelController = this.sequelize.define(
      modelName,
      formattedAttributes,
      options
    );
    return model;
  }

  private formatAttributes(
    attributes: IModelFeilds
  ): ModelAttributes<Model<any, any>> {
    const formattedAttributes: ModelAttributes<Model<any, any>> = {};

    for (const [attributeName, column] of Object.entries(attributes)) {
      formattedAttributes[attributeName] = {
        type: column.dataType,
        field: column.columnName || attributeName,
        primaryKey: column.isPrimaryKey || false,
        allowNull: column.isNullable !== false, // Default to true if not explicitly set to false
        unique: column.isUnique || false,
        autoIncrement: column.isAutoIncrement || false,
        defaultValue: column.defaultValue,
        onDelete: column.onDelete,
        onUpdate: column.onUpdate,
      };
    }

    return formattedAttributes;
  }
}

export {
  SequelizeDataTypes,
  SequelizeDataTypeInterface,
  SequelizeModelController,
};
